package resthttp

import (
	"bytes"
	"context"
	"encoding/json"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/service"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/openapi"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockTemplateAPI is a mock implementation of TemplateAPI
type MockTemplateAPI struct {
	mock.Mock
}

func (m *MockTemplateAPI) GetAllTemplates(ctx context.Context) (domain.Templates, error) {
	args := m.Called(ctx)

	return args.Get(0).(domain.Templates), args.Error(1)
}

func (m *MockTemplateAPI) GetTemplate(ctx context.Context, templateID, connectionID string) (*domain.Template, error) {
	args := m.Called(ctx, templateID, connectionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}

	return args.Get(0).(*domain.Template), args.Error(1)
}

func (m *MockTemplateAPI) SaveTemplate(ctx context.Context, template *domain.Template) error {
	args := m.Called(ctx, template)

	return args.Error(0)
}

func (m *MockTemplateAPI) UpdateTemplate(ctx context.Context, templateID, connectionID string, template *domain.Template) error {
	args := m.Called(ctx, templateID, connectionID, template)

	return args.Error(0)
}

func (m *MockTemplateAPI) DeleteTemplate(ctx context.Context, templateID, connectionID string) error {
	args := m.Called(ctx, templateID, connectionID)

	return args.Error(0)
}

// MockEMSPConnectionRepository is a mock implementation of EMSPConnectionRepository
type MockEMSPConnectionRepository struct {
	mock.Mock
}

func (m *MockEMSPConnectionRepository) Exists(ctx context.Context, countryCode, partyID, cpoURL string) (bool, error) {
	args := m.Called(ctx, countryCode, partyID, cpoURL)

	return args.Bool(0), args.Error(1)
}

func TestNewTemplate_EMSPValidation(t *testing.T) {
	tests := []struct {
		name           string
		connectionID   string
		requestBody    openapi.TemplateResponse
		mockSetup      func(*MockTemplateAPI, *MockEMSPConnectionRepository)
		expectedStatus int
	}{
		{
			name:         "Valid EMSP connection - should succeed",
			connectionID: "550e8400-e29b-41d4-a716-446655440000",
			requestBody: openapi.TemplateResponse{
				Body: strPtr("Test template body"),
				Connection: &openapi.Connection{
					CountryCode: "US",
					PartyCode:   "DOM",
					CpoUrl:      "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo",
				},
			},
			mockSetup: func(templateAPI *MockTemplateAPI, emspRepo *MockEMSPConnectionRepository) {
				emspRepo.On("Exists", mock.Anything, "US", "DOM", "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo").Return(true, nil)
				templateAPI.On("SaveTemplate", mock.Anything, mock.AnythingOfType("*domain.Template")).Return(nil)
			},
			expectedStatus: http.StatusCreated,
		},
		{
			name:         "Valid EMSP connection with different case - should succeed (case-insensitive)",
			connectionID: "550e8400-e29b-41d4-a716-446655440000",
			requestBody: openapi.TemplateResponse{
				Body: strPtr("Test template body"),
				Connection: &openapi.Connection{
					CountryCode: "us",  // lowercase
					PartyCode:   "dom", // lowercase
					CpoUrl:      "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo",
				},
			},
			mockSetup: func(templateAPI *MockTemplateAPI, emspRepo *MockEMSPConnectionRepository) {
				emspRepo.On("Exists", mock.Anything, "us", "dom", "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo").Return(true, nil)
				templateAPI.On("SaveTemplate", mock.Anything, mock.AnythingOfType("*domain.Template")).Return(nil)
			},
			expectedStatus: http.StatusCreated,
		},
		{
			name:         "Invalid EMSP connection - should fail with 400 and error message",
			connectionID: "550e8400-e29b-41d4-a716-446655440000",
			requestBody: openapi.TemplateResponse{
				Body: strPtr("Test template body"),
				Connection: &openapi.Connection{
					CountryCode: "US",
					PartyCode:   "INVALID",
					CpoUrl:      "https://invalid-url.com",
				},
			},
			mockSetup: func(_ *MockTemplateAPI, emspRepo *MockEMSPConnectionRepository) {
				emspRepo.On("Exists", mock.Anything, "US", "INVALID", "https://invalid-url.com").Return(false, nil)
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:         "Missing connection details - should succeed (no validation)",
			connectionID: "550e8400-e29b-41d4-a716-446655440000",
			requestBody: openapi.TemplateResponse{
				Body: strPtr("Test template body"),
			},
			mockSetup: func(templateAPI *MockTemplateAPI, _ *MockEMSPConnectionRepository) {
				templateAPI.On("SaveTemplate", mock.Anything, mock.AnythingOfType("*domain.Template")).Return(nil)
			},
			expectedStatus: http.StatusCreated,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockTemplateAPI := &MockTemplateAPI{}
			mockEMSPRepo := &MockEMSPConnectionRepository{}
			tt.mockSetup(mockTemplateAPI, mockEMSPRepo)

			// Create services
			emspValidator := service.NewEMSPConnectionValidator(mockEMSPRepo)
			templateSrv := service.NewTemplateSrv(mockTemplateAPI, emspValidator)
			handler := NewHandler(templateSrv)

			// Create request
			requestBody, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/api/v1/templates/"+tt.connectionID, bytes.NewReader(requestBody))
			req.Header.Set("Content-Type", "application/json")

			// Create response recorder
			rr := httptest.NewRecorder()

			// Create router and add route
			router := chi.NewRouter()
			router.Post("/api/v1/templates/{connection_id}", func(w http.ResponseWriter, r *http.Request) {
				connectionID := chi.URLParam(r, "connection_id")
				var body openapi.TemplateResponse
				json.NewDecoder(r.Body).Decode(&body)

				request := openapi.NewTemplateRequestObject{
					ConnectionId: connectionID,
					Body:         &body,
				}

				response, err := handler.NewTemplate(r.Context(), request)
				assert.NoError(t, err)

				switch resp := response.(type) {
				case openapi.NewTemplate201Response:
					w.WriteHeader(http.StatusCreated)
				case openapi.NewTemplatedefaultJSONResponse:
					w.Header().Set("Content-Type", "application/json")
					w.WriteHeader(resp.StatusCode)
					json.NewEncoder(w).Encode(resp.Body)
				default:
					w.WriteHeader(http.StatusInternalServerError)
					_ = resp
				}
			})

			// Execute request
			router.ServeHTTP(rr, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, rr.Code)

			// For validation errors, check that error message is returned
			if tt.expectedStatus == http.StatusBadRequest && tt.name == "Invalid EMSP connection - should fail with 400 and error message" {
				var errorResponse openapi.ErrorResponse
				err := json.Unmarshal(rr.Body.Bytes(), &errorResponse)
				assert.NoError(t, err)
				assert.NotNil(t, errorResponse.StatusMessage)
				assert.Contains(t, *errorResponse.StatusMessage, "EMSP connection not found")
				assert.Contains(t, *errorResponse.StatusMessage, "US")
				assert.Contains(t, *errorResponse.StatusMessage, "INVALID")
				assert.Contains(t, *errorResponse.StatusMessage, "https://invalid-url.com")
			}

			// Verify mocks
			mockTemplateAPI.AssertExpectations(t)
			mockEMSPRepo.AssertExpectations(t)
		})
	}
}
