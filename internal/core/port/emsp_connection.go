package port

import (
	"context"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
)

// EMSPConnectionRepository defines the interface for EMSP connection data access
type EMSPConnectionRepository interface {
	// FindByConnectionDetails finds an EMSP connection by country code, party ID, and CPO URL
	FindByConnectionDetails(ctx context.Context, countryCode, partyID, cpoURL string) (*domain.EMSPConnection, error)
	
	// FindByCountryCodeAndPartyID finds an EMSP connection by country code and party ID
	FindByCountryCodeAndPartyID(ctx context.Context, countryCode, partyID string) (*domain.EMSPConnection, error)
	
	// Exists checks if an EMSP connection exists with the given parameters
	Exists(ctx context.Context, countryCode, partyID, cpoURL string) (bool, error)
}
