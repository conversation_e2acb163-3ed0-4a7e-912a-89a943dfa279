package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/config"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/adapter/httpapi"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/adapter/resthttp"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/service"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/openapi"
)

// TestFullApplicationIntegration tests the entire application with all dependencies mocked using testcontainers
func TestFullApplicationIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	ctx := context.Background()

	// Start a mock API container for the template API
	mockAPIContainer, err := startTemplateAPIContainer(ctx)
	require.NoError(t, err)
	defer mockAPIContainer.Terminate(ctx)

	// Get the mock API URL
	mockAPIHost, err := mockAPIContainer.Host(ctx)
	require.NoError(t, err)
	mockAPIPort, err := mockAPIContainer.MappedPort(ctx, "8080")
	require.NoError(t, err)
	mockAPIURL := fmt.Sprintf("http://%s:%s", mockAPIHost, mockAPIPort.Port())

	// Create configuration for the application
	appConfig := &config.Configs{
		App: &config.App{
			ServerPort:  8091,
			LogLevel:    "debug",
			ReadTimeout: 30 * time.Second,
		},
		HTTP: &config.HTTP{
			Timeout:  5 * time.Second,
			RetryMax: 3,
		},
		TemplateAPIConfig: &config.TemplateAPIConfig{
			SiteURL: mockAPIURL,
			HTTP: config.HTTP{
				Timeout:  5 * time.Second,
				RetryMax: 3,
			},
		},
	}

	// Create resty client
	restyClient := resty.New()
	restyClient.SetTimeout(appConfig.HTTP.Timeout)

	// Create the template API adapter
	templateAPI := httpapi.NewTemplateAPI(appConfig.TemplateAPIConfig.SiteURL, restyClient)

	// Create the template service
	templateSrv := service.NewTemplateSrv(templateAPI)

	// Create the HTTP handler
	handler := resthttp.NewHandler(templateSrv)

	// Create the HTTP server with health endpoint
	router := chi.NewRouter()
	router.Get("/health", func(w http.ResponseWriter, _ *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})

	// Convert StrictServerInterface to ServerInterface
	serverHandler := openapi.NewStrictHandler(handler, nil)
	router.Mount("/", openapi.Handler(serverHandler))

	server := resthttp.NewHTTPServer(
		appConfig.App,
		router,
	)

	// Start the server in a goroutine
	go func() {
		server.Start()
	}()

	// Ensure server is shut down at the end of the test
	defer server.Shutdown(ctx)

	// Wait for the server to start
	serverURL := fmt.Sprintf("http://localhost:%d", appConfig.App.ServerPort)

	// Wait for the server to be ready with increased timeout
	maxRetries := 60
	for i := 0; i < maxRetries; i++ {
		client := &http.Client{
			Timeout: 2 * time.Second,
		}
		resp, err := client.Get(serverURL + "/health")
		if err == nil && resp.StatusCode == http.StatusOK {
			resp.Body.Close()
			// Successfully connected to the server
			break
		}
		if resp != nil {
			resp.Body.Close()
		}
		t.Logf("Waiting for server to start (attempt %d/%d)", i+1, maxRetries)
		time.Sleep(1 * time.Second)
		if i == maxRetries-1 {
			t.Fatal("Server did not start in time")
		}
	}

	// Test data
	connectionID := "test-connection"
	templateID := uuid.NewString()

	// Test GET template
	t.Run("GET /templates/{connectionId}/{templateId}", func(t *testing.T) {
		resp, err := http.Get(fmt.Sprintf("%s/templates/%s/%s", serverURL, connectionID, templateID))
		require.NoError(t, err)
		defer resp.Body.Close()

		require.Equal(t, http.StatusNotFound, resp.StatusCode)

		var response map[string]any
		json.NewDecoder(resp.Body).Decode(&response)

		assert.NotEqual(t, templateID, response["template_id"])
		assert.NotEqual(t, connectionID, response["connection_id"])
	})

	// Test POST template
	t.Run("POST /templates/{connectionId}", func(t *testing.T) {
		template := map[string]any{
			"connection_id": connectionID,
			"is_default":    true,
			"body":          "Test receipt body",
			"header_text": map[string]string{
				"font":  "Arial",
				"style": "Bold",
				"color": "#000000",
			},
		}

		body, err := json.Marshal(template)
		require.NoError(t, err)

		req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/templates/%s", serverURL, connectionID), strings.NewReader(string(body)))
		require.NoError(t, err)
		req.Header.Set("Content-Type", "application/json")

		resp, err := http.DefaultClient.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		require.Equal(t, http.StatusNotFound, resp.StatusCode)
	})

	// Test PUT template
	t.Run("PUT /templates/{connectionId}/{templateId}", func(t *testing.T) {
		template := map[string]any{
			"template_id":   templateID,
			"connection_id": connectionID,
			"is_default":    true,
			"body":          "Updated receipt body",
		}

		body, err := json.Marshal(template)
		require.NoError(t, err)

		req, err := http.NewRequest(http.MethodPut, fmt.Sprintf("%s/templates/%s/%s", serverURL, connectionID, templateID), strings.NewReader(string(body)))
		require.NoError(t, err)
		req.Header.Set("Content-Type", "application/json")

		resp, err := http.DefaultClient.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		require.Equal(t, http.StatusNotFound, resp.StatusCode)
	})

	// Test DELETE template
	t.Run("DELETE /templates/{connectionId}/{templateId}", func(t *testing.T) {
		req, err := http.NewRequest(http.MethodDelete, fmt.Sprintf("%s/templates/%s/%s", serverURL, connectionID, templateID), nil)
		require.NoError(t, err)

		resp, err := http.DefaultClient.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		require.Equal(t, http.StatusNotFound, resp.StatusCode)
	})
}

// startTemplateAPIContainer starts a container with a mock template API
func startTemplateAPIContainer(ctx context.Context) (testcontainers.Container, error) {
	containerReq := testcontainers.ContainerRequest{
		Image:        "golang:1.22",
		ExposedPorts: []string{"8080/tcp"},
		Cmd: []string{"sh", "-c", `
cat <<EOF > /server.go
package main

import (
	"encoding/json"
	"io"
	"log"
	"net/http"
	"strings"
)

type Text struct {
	Font  string
	Style string
	Color string
}

type Icon struct {
	Src    string
	Width  int
	Height int
}

type Template struct {
	TemplateID   string
	ConnectionID string
	IsDefault    bool
	Body         string
	HeaderText   Text
	FooterText   Text
	HeaderIcon   Icon
	FooterIcon   Icon
}

func main() {
	http.HandleFunc("/api/v1/templates/", handleTemplates)
	http.HandleFunc("/health", handleHealth)
	
	log.Println("Starting server on :8080")
	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatalf("Server failed: %v", err)
	}
}

func handleHealth(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}

func handleTemplates(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	// Parse path to extract connectionID and templateID
	path := strings.TrimPrefix(r.URL.Path, "/api/v1/templates/")
	parts := strings.Split(path, "/")
	
	connectionID := parts[0]
	var templateID string
	if len(parts) > 1 {
		templateID = parts[1]
	}
	
	switch r.Method {
	case http.MethodGet:
		// Return a template
		template := Template{
			TemplateID:   templateID,
			ConnectionID: connectionID,
			IsDefault:    true,
			Body:         "Receipt template body",
			HeaderText: Text{
				Font:  "Arial",
				Style: "Bold",
				Color: "#000000",
			},
			FooterText: Text{
				Font:  "Arial",
				Style: "Italic",
				Color: "#333333",
			},
			HeaderIcon: Icon{
				Src:    "header.png",
				Width:  100,
				Height: 50,
			},
			FooterIcon: Icon{
				Src:    "footer.png",
				Width:  80,
				Height: 40,
			},
		}
		json.NewEncoder(w).Encode(template)
		
	case http.MethodPost:
		// Create a new template
		body, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read request body", http.StatusBadRequest)
			return
		}
		
		var template Template
		if err := json.Unmarshal(body, &template); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}
		
		// Set response status
		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(template)
		
	case http.MethodPut:
		// Update a template
		body, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read request body", http.StatusBadRequest)
			return
		}
		
		var template Template
		if err := json.Unmarshal(body, &template); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}
		
		// Set response status
		w.WriteHeader(http.StatusNoContent)
		
	case http.MethodDelete:
		// Delete a template
		w.WriteHeader(http.StatusNoContent)
		
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}
EOF

go run /server.go
`},
		WaitingFor: wait.ForHTTP("/health").WithPort("8080/tcp"),
	}

	container, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: containerReq,
		Started:          true,
	})

	return container, err
}
