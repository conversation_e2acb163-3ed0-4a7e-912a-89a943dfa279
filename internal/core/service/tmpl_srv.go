package service

import (
	"context"

	"github.com/google/uuid"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/port"
)

var _ port.TemplateManager = (*TemplateSrv)(nil)

type TemplateSrv struct {
	manager   port.TemplateAPI
	validator *EMSPConnectionValidator
}

func NewTemplateSrv(manager port.TemplateAPI, validator *EMSPConnectionValidator) TemplateSrv {
	return TemplateSrv{
		manager:   manager,
		validator: validator,
	}
}

func (t TemplateSrv) RetrieveTemplate(ctx context.Context, templateID, connectionID string) (*domain.Template, error) {
	return t.manager.GetTemplate(ctx, templateID, connectionID)
}

func (t TemplateSrv) NewTemplate(ctx context.Context, template *domain.Template) error {
	// Validate EMSP connection if connection details are provided
	if t.validator != nil && template.Connection.ContryCode != "" && template.Connection.PartyCode != "" && template.Connection.CPOURL != "" {
		err := t.validator.ValidateConnection(ctx, template.Connection.ContryCode, template.Connection.PartyCode, template.Connection.CPOURL)
		if err != nil {
			return err
		}
	}

	template.TemplateID = uuid.NewString()

	return t.manager.SaveTemplate(ctx, template)
}

func (t TemplateSrv) UpdateTemplate(ctx context.Context, template *domain.Template) error {
	// Validate EMSP connection if connection details are provided
	if t.validator != nil && template.Connection.ContryCode != "" && template.Connection.PartyCode != "" && template.Connection.CPOURL != "" {
		err := t.validator.ValidateConnection(ctx, template.Connection.ContryCode, template.Connection.PartyCode, template.Connection.CPOURL)
		if err != nil {
			return err
		}
	}

	return t.manager.UpdateTemplate(ctx, template.TemplateID, template.ConnectionID, template)
}

func (t TemplateSrv) RemoveTemplate(ctx context.Context, templateID, connectionID string) error {
	return t.manager.DeleteTemplate(ctx, templateID, connectionID)
}

func (t TemplateSrv) ListTemplate(ctx context.Context) (domain.Templates, error) {
	return t.manager.GetAllTemplates(ctx)
}
