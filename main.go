package main

import (
	"context"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/config"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/adapter/httpapi"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/adapter/mongodb"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/adapter/resthttp"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/common"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/service"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/openapi"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/go-resty/resty/v2"
	"github.com/inv-cloud-platform/hub-com-auth-go/hubauth"
	"github.com/inv-cloud-platform/hub-com-tools-go/hubmiddlewares"
	"github.com/inv-cloud-platform/hub-com-tools-go/hubmongo"

	"github.com/rs/zerolog/log"
)

func main() {
	log.Info().Msg("application starting ...")
	configs := config.Load()
	common.SetupLogger(configs.App.LogLevel)

	appCtx := context.Background()
	mongoClient, errMongo := hubmongo.Connect(appCtx)
	if errMongo != nil {
		log.Fatal().Err(errMongo).Msg("unable to connect to mongodb")
	}

	auth := hubauth.NewAuthorization(
		configs.Auth.KeycloakHost,
		configs.Auth.KeycloakRealm,
	)

	oapiSpec, errSw := openapi.GetSwagger()
	if errSw != nil {
		log.Warn().Err(errSw).Msg("unable to fetch openapi spec")
	}

	httpClient := initRestyClient(configs.HTTP)
	templateApi := httpapi.NewTemplateAPI(configs.TemplateAPIConfig.SiteURL, httpClient)

	// Create EMSP connection repository and validator
	emspRepo := mongodb.NewEMSPConnectionRepository(mongoClient, configs.MongoDB)
	emspValidator := service.NewEMSPConnectionValidator(emspRepo)

	templateSrv := service.NewTemplateSrv(templateApi, emspValidator)
	apiHandler := resthttp.NewHandler(templateSrv)
	// Use NewStrictHandler to convert from StrictServerInterface to ServerInterface
	httpServer := resthttp.NewHTTPServer(
		configs.App,
		openapi.Handler(openapi.NewStrictHandler(apiHandler, nil)),
		resthttp.MetricsCollector,
		hubmiddlewares.Proxy("/proxy/myApi", "http://localhost:8080", &hubmiddlewares.ProxyOptions{
			ProxyLogic: hubmiddlewares.PROXY_SELECTED,
			Endpoints: map[string][]string{
				"/something": {http.MethodGet},
			},
		}),
		hubmiddlewares.RequestTrack("/health"),
		hubmiddlewares.Health("/health"),
		hubmiddlewares.Spec(
			hubmiddlewares.SpecOptionsTitle(oapiSpec.Info.Title),
			hubmiddlewares.SpecOptionsObjectSpec(oapiSpec),
		),
		auth.Middleware(&hubauth.MiddlewareConfig{
			Strategy: hubauth.AllOf,
			Domains:  []string{common.Domain},
		}),
	)
	go httpServer.Start()
	defer func(ctx context.Context) {
		errS := httpServer.Shutdown(ctx)
		if errS != nil {
			log.Err(errS).Msg("http server shutdown error")
		}
	}(appCtx)

	ctx, stop := signal.NotifyContext(appCtx, os.Interrupt, os.Kill, syscall.SIGTERM, syscall.SIGINT)
	defer stop()
	log.Info().Msg("waiting for app exiting conditions")

	<-ctx.Done()
	log.Info().Msg("application stopping ...")
}

func initRestyClient(cfg *config.HTTP) *resty.Client {
	client := resty.New()
	client.
		SetRetryCount(cfg.RetryMax).
		SetTimeout(cfg.Timeout)

	return client
}
