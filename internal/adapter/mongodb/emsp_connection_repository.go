package mongodb

import (
	"context"
	"fmt"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/config"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/port"

	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
)

// EMSPConnectionRepository implements the port.EMSPConnectionRepository interface
var _ port.EMSPConnectionRepository = (*EMSPConnectionRepository)(nil)

type EMSPConnectionRepository struct {
	client     *mongo.Client
	database   string
	collection string
}

// NewEMSPConnectionRepository creates a new instance of EMSPConnectionRepository
func NewEMSPConnectionRepository(client *mongo.Client, cfg *config.Mongodb) *EMSPConnectionRepository {
	return &EMSPConnectionRepository{
		client:     client,
		database:   cfg.Database,
		collection: cfg.Collection,
	}
}

// FindByConnectionDetails finds an EMSP connection by country code, party ID, and CPO URL
func (r *EMSPConnectionRepository) FindByConnectionDetails(ctx context.Context, countryCode, partyID, cpoURL string) (*domain.EMSPConnection, error) {
	collection := r.client.Database(r.database).Collection(r.collection)

	filter := bson.M{
		"country_code": countryCode,
		"party_code":   partyID,
		"cpo_url":      cpoURL,
	}

	var connection domain.EMSPConnection
	err := collection.FindOne(ctx, filter).Decode(&connection)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("EMSP connection not found for country_code: %s, party_id: %s, cpo_url: %s", countryCode, partyID, cpoURL)
		}
		return nil, fmt.Errorf("failed to find EMSP connection: %w", err)
	}

	return &connection, nil
}

// FindByCountryCodeAndPartyID finds an EMSP connection by country code and party ID
func (r *EMSPConnectionRepository) FindByCountryCodeAndPartyID(ctx context.Context, countryCode, partyID string) (*domain.EMSPConnection, error) {
	collection := r.client.Database(r.database).Collection(r.collection)

	filter := bson.M{
		"country_code": countryCode,
		"party_code":   partyID,
	}

	var connection domain.EMSPConnection
	err := collection.FindOne(ctx, filter).Decode(&connection)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("EMSP connection not found for country_code: %s, party_id: %s", countryCode, partyID)
		}
		return nil, fmt.Errorf("failed to find EMSP connection: %w", err)
	}

	return &connection, nil
}

// Exists checks if an EMSP connection exists with the given parameters
func (r *EMSPConnectionRepository) Exists(ctx context.Context, countryCode, partyID, cpoURL string) (bool, error) {
	collection := r.client.Database(r.database).Collection(r.collection)

	filter := bson.M{
		"country_code": countryCode,
		"party_code":   partyID,
		"cpo_url":      cpoURL,
	}

	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, fmt.Errorf("failed to check EMSP connection existence: %w", err)
	}

	return count > 0, nil
}
