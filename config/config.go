package config

import (
	"time"

	env "github.com/caarlos0/env/v11"
	"github.com/rs/zerolog/log"
)

type Configs struct {
	Auth              *Auth
	App               *App
	HTTP              *HTTP
	TemplateAPIConfig *TemplateAPIConfig
	MongoDB           *Mongodb
}

func Load() *Configs {
	cfg := &Configs{
		Auth:              &Auth{},
		App:               &App{},
		HTTP:              &HTTP{},
		TemplateAPIConfig: &TemplateAPIConfig{},
		MongoDB:           &Mongodb{},
	}
	if err := env.Parse(cfg); err != nil {
		log.Fatal().Err(err).Msg("failed to load configs")
	}

	return cfg
}

type Auth struct {
	KeycloakHost    string `env:"KC_HOST" envDefault:"https://auth.hub-dev.invenco.com"`
	KeycloakRealm   string `env:"KC_REALM" envDefault:"invenco-hub"`
	ClientID        string `env:"AUTH_CLIENT_ID"`
	ClientSecret    string `env:"AUTH_CLIENT_SECRET"`
	ServiceUsername string `env:"AUTH_SERVICE_USERNAME"`
	ServicePassword string `env:"AUTH_SERVICE_PASSWORD"`
}

type HTTP struct {
	Timeout  time.Duration `env:"HTTP_CLIENT_TIMEOUT"   envDefault:"30s"`
	RetryMax int           `env:"HTTP_CLIENT_RETRY_MAX" envDefault:"3"`
}

type App struct {
	LogLevel    string        `env:"LOG_LEVEL" envDefault:"debug"`
	ServerPort  int           `env:"SERVER_PORT" envDefault:"8080"`
	ReadTimeout time.Duration `env:"SERVER_READ_TIMEOUT" envDefault:"500s"`
}

type TemplateAPIConfig struct {
	SiteURL string `env:"TEMPLATE_API_URL" envDefault:"http://localhost:8087"`
	HTTP
}

type Mongodb struct {
	Database   string `env:"MONGO_DATABASE" envDefault:"fe-secure"`
	Collection string `env:"MONGO_COLLECTION" envDefault:"connections"`
}
