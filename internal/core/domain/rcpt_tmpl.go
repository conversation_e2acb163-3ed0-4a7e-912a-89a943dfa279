package domain

type Templates []Template

type Template struct {
	TemplateID   string     `json:"template_id"`
	ConnectionID string     `json:"connection_id"`
	IsDefault    bool       `json:"is_default"`
	Body         string     `json:"body"`
	HeaderText   Text       `json:"header_text"`
	FooterText   Text       `json:"footer_text"`
	HeaderIcon   Icon       `json:"header_logo"`
	FooterIcon   Icon       `json:"footer_logo"`
	Connection   Connection `json:"connection"`
	LastUpdated  string     `json:"last_updated"`
}

type Text struct {
	Font  string `json:"font"`
	Style string `json:"style"`
	Color string `json:"color"`
	Value string `json:"value"`
}

type Icon struct {
	Src    string `json:"src"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
	Name   string `json:"name"`
}

type Connection struct {
	ConnectionName string `json:"connection_name"`
	CPOURL         string `json:"cpo_url"`
	ContryCode     string `json:"country_code"`
	PartyCode      string `json:"party_code"`
}
