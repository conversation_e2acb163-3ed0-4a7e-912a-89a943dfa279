package domain

import "errors"

var (
	ErrUserAlreadyExists = errors.New("user already exists")

	ErrUnableToFetchScopes = errors.New("unable to fetch scopes from lookup api")
	ErrForbiddenAction     = errors.New("user is not allowed to execute action")
	ErrForbiddenResource   = errors.New("user is not allowed to access resource")

	// EMSP Connection validation errors
	ErrEMSPConnectionNotFound = errors.New("EMSP connection not found")
	ErrInvalidConnectionData  = errors.New("invalid connection data")
)

// EMSPConnectionValidationError represents an EMSP connection validation error
type EMSPConnectionValidationError struct {
	CountryCode string
	PartyID     string
	CPOURL      string
	Message     string
}

func (e *EMSPConnectionValidationError) Error() string {
	return e.Message
}

// IsEMSPConnectionValidationError checks if an error is an EMSP connection validation error
func IsEMSPConnectionValidationError(err error) bool {
	_, ok := err.(*EMSPConnectionValidationError)
	return ok
}
