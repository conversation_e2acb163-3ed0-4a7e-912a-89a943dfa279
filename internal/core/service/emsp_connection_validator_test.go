package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
)

// MockEMSPConnectionRepository is a mock implementation of EMSPConnectionRepository
type MockEMSPConnectionRepository struct {
	mock.Mock
}

func (m *MockEMSPConnectionRepository) FindByConnectionDetails(ctx context.Context, countryCode, partyID, cpoURL string) (*domain.EMSPConnection, error) {
	args := m.Called(ctx, countryCode, partyID, cpoURL)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.EMSPConnection), args.Error(1)
}

func (m *MockEMSPConnectionRepository) FindByCountryCodeAndPartyID(ctx context.Context, countryCode, partyID string) (*domain.EMSPConnection, error) {
	args := m.Called(ctx, countryCode, partyID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.EMSPConnection), args.Error(1)
}

func (m *MockEMSPConnectionRepository) Exists(ctx context.Context, countryCode, partyID, cpoURL string) (bool, error) {
	args := m.Called(ctx, countryCode, partyID, cpoURL)
	return args.Bool(0), args.Error(1)
}

func TestEMSPConnectionValidator_ValidateConnection(t *testing.T) {
	tests := []struct {
		name        string
		countryCode string
		partyID     string
		cpoURL      string
		mockSetup   func(*MockEMSPConnectionRepository)
		expectError bool
		errorType   string
	}{
		{
			name:        "Valid connection exists",
			countryCode: "US",
			partyID:     "DOM",
			cpoURL:      "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo",
			mockSetup: func(m *MockEMSPConnectionRepository) {
				m.On("Exists", mock.Anything, "US", "DOM", "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo").Return(true, nil)
			},
			expectError: false,
		},
		{
			name:        "Connection does not exist",
			countryCode: "US",
			partyID:     "DOM",
			cpoURL:      "https://invalid-url.com",
			mockSetup: func(m *MockEMSPConnectionRepository) {
				m.On("Exists", mock.Anything, "US", "DOM", "https://invalid-url.com").Return(false, nil)
			},
			expectError: true,
			errorType:   "EMSPConnectionValidationError",
		},
		{
			name:        "Empty country code",
			countryCode: "",
			partyID:     "DOM",
			cpoURL:      "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo",
			mockSetup:   func(m *MockEMSPConnectionRepository) {},
			expectError: true,
		},
		{
			name:        "Empty party ID",
			countryCode: "US",
			partyID:     "",
			cpoURL:      "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo",
			mockSetup:   func(m *MockEMSPConnectionRepository) {},
			expectError: true,
		},
		{
			name:        "Empty CPO URL",
			countryCode: "US",
			partyID:     "DOM",
			cpoURL:      "",
			mockSetup:   func(m *MockEMSPConnectionRepository) {},
			expectError: true,
		},
		{
			name:        "Repository error",
			countryCode: "US",
			partyID:     "DOM",
			cpoURL:      "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo",
			mockSetup: func(m *MockEMSPConnectionRepository) {
				m.On("Exists", mock.Anything, "US", "DOM", "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo").Return(false, errors.New("database error"))
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := &MockEMSPConnectionRepository{}
			tt.mockSetup(mockRepo)

			validator := NewEMSPConnectionValidator(mockRepo)
			err := validator.ValidateConnection(context.Background(), tt.countryCode, tt.partyID, tt.cpoURL)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorType == "EMSPConnectionValidationError" {
					assert.True(t, domain.IsEMSPConnectionValidationError(err))
				}
			} else {
				assert.NoError(t, err)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestEMSPConnectionValidator_ValidateConnectionByCountryAndParty(t *testing.T) {
	tests := []struct {
		name        string
		countryCode string
		partyID     string
		mockSetup   func(*MockEMSPConnectionRepository)
		expectError bool
	}{
		{
			name:        "Valid connection exists",
			countryCode: "US",
			partyID:     "DOM",
			mockSetup: func(m *MockEMSPConnectionRepository) {
				connection := &domain.EMSPConnection{
					CountryCode: "US",
					PartyCode:   "DOM",
				}
				m.On("FindByCountryCodeAndPartyID", mock.Anything, "US", "DOM").Return(connection, nil)
			},
			expectError: false,
		},
		{
			name:        "Connection does not exist",
			countryCode: "US",
			partyID:     "INVALID",
			mockSetup: func(m *MockEMSPConnectionRepository) {
				m.On("FindByCountryCodeAndPartyID", mock.Anything, "US", "INVALID").Return(nil, errors.New("not found"))
			},
			expectError: true,
		},
		{
			name:        "Empty country code",
			countryCode: "",
			partyID:     "DOM",
			mockSetup:   func(m *MockEMSPConnectionRepository) {},
			expectError: true,
		},
		{
			name:        "Empty party ID",
			countryCode: "US",
			partyID:     "",
			mockSetup:   func(m *MockEMSPConnectionRepository) {},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := &MockEMSPConnectionRepository{}
			tt.mockSetup(mockRepo)

			validator := NewEMSPConnectionValidator(mockRepo)
			err := validator.ValidateConnectionByCountryAndParty(context.Background(), tt.countryCode, tt.partyID)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}
