# EMSP Connection Validation Error Responses

This document describes the error response format when EMSP connection validation fails in the EV Receipt Template BFF service.

## Overview

When creating or updating templates via POST and PUT endpoints, the service validates that the EMSP connection exists in the MongoDB database. If validation fails, a structured error response is returned to the client.

## Affected Endpoints

- `POST /api/v1/templates/{connection_id}` - Create new template
- `PUT /api/v1/templates/{connection_id}/{template_id}` - Update existing template

## Validation Logic

The service validates EMSP connections when all three fields are present in the request:
- `country_code`
- `party_code` (mapped from `party_id` in the database)
- `cpo_url`

If any of these fields are missing or empty, validation is skipped (backward compatibility).

## Error Response Format

### HTTP Status Code
- **400 Bad Request** - When EMSP connection validation fails
- **500 Internal Server Error** - For other system errors

### Response Body Structure

```json
{
  "status_message": "EMSP connection not found for country_code: {country_code}, party_id: {party_id}, cpo_url: {cpo_url}",
  "status_code": 400,
  "timestamp": "2023-10-29T12:00:00Z"
}
```

**Note**: The `status_code` and `timestamp` fields are optional and may not always be present in the response.

## Example Error Responses

### 1. Invalid EMSP Connection

**Request:**
```json
POST /api/v1/templates/550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json

{
  "body": "Test template body",
  "connection": {
    "country_code": "US",
    "party_code": "INVALID",
    "cpo_url": "https://invalid-url.com"
  }
}
```

**Response:**
```json
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "status_message": "EMSP connection not found for country_code: US, party_id: INVALID, cpo_url: https://invalid-url.com"
}
```

### 2. Valid EMSP Connection

**Request:**
```json
POST /api/v1/templates/550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json

{
  "body": "Test template body",
  "connection": {
    "country_code": "US",
    "party_code": "DOM",
    "cpo_url": "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo"
  }
}
```

**Response:**
```json
HTTP/1.1 201 Created
```

### 3. Missing Connection Data (No Validation)

**Request:**
```json
POST /api/v1/templates/550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json

{
  "body": "Test template body"
}
```

**Response:**
```json
HTTP/1.1 201 Created
```

## Error Message Format

The error message follows this pattern:
```
EMSP connection not found for country_code: {country_code}, party_id: {party_id}, cpo_url: {cpo_url}
```

Where:
- `{country_code}` - The country code from the request
- `{party_id}` - The party ID from the request  
- `{cpo_url}` - The CPO URL from the request

## Client Error Handling

Clients should:

1. **Check HTTP Status Code**: Look for `400` status code to identify validation errors
2. **Parse Error Message**: Extract the `status_message` field from the JSON response
3. **Display User-Friendly Message**: Show appropriate error message to users
4. **Log Details**: Log the full error response for debugging

### Example Client Code (JavaScript)

```javascript
try {
  const response = await fetch('/api/v1/templates/connection-id', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(templateData)
  });

  if (response.status === 400) {
    const errorData = await response.json();
    console.error('EMSP Validation Error:', errorData.status_message);
    // Show user-friendly error message
    showError('Invalid EMSP connection. Please check your connection details.');
  } else if (!response.ok) {
    console.error('Server Error:', response.status);
    showError('An unexpected error occurred. Please try again.');
  } else {
    console.log('Template created successfully');
  }
} catch (error) {
  console.error('Network Error:', error);
  showError('Network error. Please check your connection.');
}
```

## Database Query

The validation performs the following MongoDB query:
```javascript
db.connections.countDocuments({
  "country_code": "US",
  "party_code": "DOM", 
  "cpo_url": "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo"
})
```

If the count is 0, validation fails and the error response is returned.
