package service

import (
	"context"
	"fmt"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/port"
)

// EMSPConnectionValidator provides validation services for EMSP connections
type EMSPConnectionValidator struct {
	emspRepo port.EMSPConnectionRepository
}

// NewEMSPConnectionValidator creates a new instance of EMSPConnectionValidator
func NewEMSPConnectionValidator(emspRepo port.EMSPConnectionRepository) *EMSPConnectionValidator {
	return &EMSPConnectionValidator{
		emspRepo: emspRepo,
	}
}

// ValidateConnection validates if an EMSP connection exists with the given parameters
func (v *EMSPConnectionValidator) ValidateConnection(ctx context.Context, countryCode, partyID, cpoURL string) error {
	if countryCode == "" {
		return fmt.Errorf("country_code is required")
	}
	
	if partyID == "" {
		return fmt.Errorf("party_id is required")
	}
	
	if cpoURL == "" {
		return fmt.Errorf("cpo_url is required")
	}
	
	exists, err := v.emspRepo.Exists(ctx, countryCode, partyID, cpoURL)
	if err != nil {
		return fmt.Errorf("failed to validate EMSP connection: %w", err)
	}
	
	if !exists {
		return fmt.Errorf("EMSP connection not found for country_code: %s, party_id: %s, cpo_url: %s", countryCode, partyID, cpoURL)
	}
	
	return nil
}

// ValidateConnectionByCountryAndParty validates if an EMSP connection exists with country code and party ID
func (v *EMSPConnectionValidator) ValidateConnectionByCountryAndParty(ctx context.Context, countryCode, partyID string) error {
	if countryCode == "" {
		return fmt.Errorf("country_code is required")
	}
	
	if partyID == "" {
		return fmt.Errorf("party_id is required")
	}
	
	_, err := v.emspRepo.FindByCountryCodeAndPartyID(ctx, countryCode, partyID)
	if err != nil {
		return fmt.Errorf("EMSP connection validation failed: %w", err)
	}
	
	return nil
}
