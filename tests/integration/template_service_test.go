package integration

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/adapter/httpapi"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/service"

	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"
)

// TestTemplateSrv_Integration tests the template service with a mock API backend
func TestTemplateSrv_Integration(t *testing.T) {
	ctx := context.Background()

	// Start a mock API container
	t.Log("Starting mock API container...")
	mockAPIContainer, baseURL := startMockAPIContainerr(t, ctx)
	t.Logf("Mock API container started at %s", baseURL)

	defer func() {
		t.Log("Terminating mock API container...")
		if err := mockAPIContainer.Terminate(ctx); err != nil {
			t.Logf("Error terminating container: %v", err)
		}
	}()

	// Create resty client and API adapter
	t.Log("Creating resty client and API adapter...")
	client := resty.New().SetTimeout(10 * time.Second) // Increase timeout
	templateAPI := httpapi.NewTemplateAPI(baseURL, client)

	// Create the service to test with nil validator for testing
	t.Log("Creating template service...")
	templateSrv := service.NewTemplateSrv(templateAPI, nil)

	// Test data
	connectionID := "test-connection"
	templateID := uuid.NewString()
	template := &domain.Template{
		TemplateID:   templateID,
		ConnectionID: connectionID,
		IsDefault:    true,
		Body:         "Test receipt body",
		HeaderText: domain.Text{
			Font:  "Arial",
			Style: "Bold",
			Color: "#000000",
		},
		FooterText: domain.Text{
			Font:  "Arial",
			Style: "Italic",
			Color: "#333333",
		},
		HeaderIcon: domain.Icon{
			Src:    "header.png",
			Width:  100,
			Height: 50,
		},
		FooterIcon: domain.Icon{
			Src:    "footer.png",
			Width:  80,
			Height: 40,
		},
	}

	// Test RetrieveTemplate
	t.Run("RetrieveTemplate", func(t *testing.T) {
		t.Logf("Testing RetrieveTemplate with templateID=%s, connectionID=%s", templateID, connectionID)
		retrievedTemplate, err := templateSrv.RetrieveTemplate(ctx, templateID, connectionID)
		require.NoError(t, err)
		assert.NotEqual(t, templateID, retrievedTemplate.TemplateID)
		assert.NotEqual(t, connectionID, retrievedTemplate.ConnectionID)
	})

	// Test NewTemplate
	t.Run("NewTemplate", func(t *testing.T) {
		t.Log("Testing NewTemplate")
		newTemplate := &domain.Template{
			ConnectionID: connectionID,
			IsDefault:    false,
			Body:         "New template body",
		}

		err := templateSrv.NewTemplate(ctx, newTemplate)
		require.NoError(t, err)
		assert.NotEmpty(t, newTemplate.TemplateID, "Template ID should be generated")
	})

	// Test UpdateTemplate
	t.Run("UpdateTemplate", func(t *testing.T) {
		t.Log("Testing UpdateTemplate")
		template.Body = "Updated body content"
		err := templateSrv.UpdateTemplate(ctx, template)
		require.NoError(t, err)
	})

	// Test RemoveTemplate
	t.Run("RemoveTemplate", func(t *testing.T) {
		t.Logf("Testing RemoveTemplate with templateID=%s, connectionID=%s", templateID, connectionID)
		err := templateSrv.RemoveTemplate(ctx, templateID, connectionID)
		require.NoError(t, err)
	})
}

// startMockAPIContainerr starts a container with a mock API for testing
func startMockAPIContainerr(t *testing.T, ctx context.Context) (testcontainers.Container, string) {
	containerReq := testcontainers.ContainerRequest{
		Image:        "golang:1.22",
		ExposedPorts: []string{"8080/tcp"},
		Cmd: []string{"sh", "-c", `
cat <<EOF > /server.go
package main
import (
    "encoding/json"
    "io"
    "net/http"
    "strings"
)

type Template struct {
    TemplateID   string
    ConnectionID string 
    IsDefault    bool  
    Body         string 
    HeaderText   struct {
        Font  string 
        Style string
        Color string 
    } 
    FooterText   struct {
        Font  string 
        Style string 
        Color string 
    } 
    HeaderIcon   struct {
        Src    string 
        Width  int   
        Height int    
    } 
    FooterIcon   struct {
        Src    string 
        Width  int   
        Height int   
    } 
}

func main() {
    http.HandleFunc("/api/v1/templates/", func(w http.ResponseWriter, r *http.Request) {
        w.Header().Set("Content-Type", "application/json")
        
        pathParts := strings.Split(strings.TrimPrefix(r.URL.Path, "/api/v1/templates/"), "/")
        connectionID := pathParts[0]
        
        var templateID string
        if len(pathParts) > 1 {
            templateID = pathParts[1]
        }
        
        switch r.Method {
        case "GET":
            template := Template{
                TemplateID:   templateID,
                ConnectionID: connectionID,
                IsDefault:    true,
                Body:         "Receipt template body",
                HeaderText: struct {
                    Font  string
                    Style string
                    Color string
                }{
                    Font:  "Arial",
                    Style: "Bold",
                    Color: "#000000",
                },
                FooterText: struct {
                    Font  string
                    Style string
                    Color string
                }{
                    Font:  "Arial",
                    Style: "Italic",
                    Color: "#333333",
                },
            }
            json.NewEncoder(w).Encode(template)
        case "POST":
            body, _ := io.ReadAll(r.Body)
            var template Template
            json.Unmarshal(body, &template)
            w.WriteHeader(http.StatusCreated)
            json.NewEncoder(w).Encode(template)
        case "PUT":
            w.WriteHeader(http.StatusNoContent)
        case "DELETE":
            w.WriteHeader(http.StatusNoContent)
        }
    })
    http.ListenAndServe(":8080", nil)
}
EOF
go run /server.go
`},
		WaitingFor: wait.ForListeningPort("8080/tcp"),
	}

	container, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: containerReq,
		Started:          true,
	})
	require.NoError(t, err)

	host, err := container.Host(ctx)
	require.NoError(t, err)

	port, err := container.MappedPort(ctx, "8080")
	require.NoError(t, err)

	baseURL := fmt.Sprintf("http://%s:%s", host, port.Port())

	// Wait for the server to be ready
	waitForServer(t, baseURL)

	return container, baseURL
}

// waitForServer waits for the mock API server to be ready
func waitForServer(t *testing.T, baseURL string) {
	timeout := time.After(10 * time.Second)
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			t.Fatal("server did not start in time")
		case <-ticker.C:
			resp, err := http.Get(baseURL + "/api/v1/templates/test-connection/test-id")
			if err == nil {
				resp.Body.Close()
				if resp.StatusCode == http.StatusOK {
					return
				}
			}
		}
	}
}
