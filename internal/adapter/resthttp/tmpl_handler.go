package resthttp

import (
	"context"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/openapi"

	"github.com/google/uuid"
)

// DeleteTemplate implements openapi.StrictServerInterface.
func (h *Handler) DeleteTemplate(ctx context.Context, request openapi.DeleteTemplateRequestObject) (openapi.DeleteTemplateResponseObject, error) {
	templateID := request.TemplateId
	connectionID := request.ConnectionId

	if _, err := uuid.Parse(templateID); err != nil {
		return openapi.DeleteTemplate401JSONResponse{}, nil
	}
	if _, err := uuid.Parse(connectionID); err != nil {
		return openapi.DeleteTemplate401JSONResponse{}, nil
	}

	err := h.tmplSrv.RemoveTemplate(ctx, templateID, connectionID)
	if err != nil {
		return openapi.DeleteTemplatedefaultJSONResponse{
			Body:       openapi.ErrorResponse{StatusMessage: strPtr("something went wrong")},
			StatusCode: 500,
		}, nil
	}

	return openapi.DeleteTemplate204Response{}, nil
}

// GetTemplate implements openapi.StrictServerInterface.
func (h *Handler) GetTemplate(ctx context.Context, request openapi.GetTemplateRequestObject) (openapi.GetTemplateResponseObject, error) {
	templateID := request.TemplateId
	connectionID := request.ConnectionId

	if _, err := uuid.Parse(templateID); err != nil {
		return openapi.GetTemplate401JSONResponse{}, nil
	}
	if _, err := uuid.Parse(connectionID); err != nil {
		return openapi.GetTemplate401JSONResponse{}, nil
	}

	template, err := h.tmplSrv.RetrieveTemplate(ctx, templateID, connectionID)
	if err != nil {
		return openapi.GetTemplatedefaultJSONResponse{
			Body:       openapi.ErrorResponse{StatusMessage: strPtr("something went wrong")},
			StatusCode: 500,
		}, nil
	}

	return openapi.GetTemplate200JSONResponse(*ToOpenAPIModel(template)), nil
}

// NewTemplate implements openapi.StrictServerInterface.
func (h *Handler) NewTemplate(ctx context.Context, request openapi.NewTemplateRequestObject) (openapi.NewTemplateResponseObject, error) {
	connectionID := request.ConnectionId

	if _, err := uuid.Parse(connectionID); err != nil {
		return openapi.NewTemplate400Response{}, nil
	}

	var template domain.Template
	if request.Body == nil {
		return openapi.NewTemplate400Response{}, nil
	}
	// Convert OpenAPI model to domain model
	template = *ToTemplateModel(request.Body)
	template.ConnectionID = connectionID

	if err := h.tmplSrv.NewTemplate(ctx, &template); err != nil {
		// Check if it's an EMSP connection validation error
		if domain.IsEMSPConnectionValidationError(err) {
			return openapi.NewTemplatedefaultJSONResponse{
				Body:       openapi.ErrorResponse{StatusMessage: strPtr(err.Error())},
				StatusCode: 400,
			}, nil
		}
		return openapi.NewTemplatedefaultJSONResponse{
			Body:       openapi.ErrorResponse{StatusMessage: strPtr("something went wrong")},
			StatusCode: 500,
		}, nil
	}

	return openapi.NewTemplate201Response{}, nil
}

// UpdateTemplate implements openapi.StrictServerInterface.
func (h *Handler) UpdateTemplate(ctx context.Context, request openapi.UpdateTemplateRequestObject) (openapi.UpdateTemplateResponseObject, error) {
	templateID := request.TemplateId
	connectionID := request.ConnectionId

	if _, err := uuid.Parse(templateID); err != nil {
		return openapi.UpdateTemplate400Response{}, nil
	}
	if _, err := uuid.Parse(connectionID); err != nil {
		return openapi.UpdateTemplate400Response{}, nil
	}

	var template domain.Template
	if request.Body == nil {
		return openapi.UpdateTemplate400Response{}, nil
	}
	template = *ToTemplateModel(request.Body)
	template.TemplateID = templateID
	template.ConnectionID = connectionID

	if err := h.tmplSrv.UpdateTemplate(ctx, &template); err != nil {
		// Check if it's an EMSP connection validation error
		if domain.IsEMSPConnectionValidationError(err) {
			return openapi.UpdateTemplatedefaultJSONResponse{
				Body:       openapi.ErrorResponse{StatusMessage: strPtr(err.Error())},
				StatusCode: 400,
			}, nil
		}
		return openapi.UpdateTemplatedefaultJSONResponse{
			Body:       openapi.ErrorResponse{StatusMessage: strPtr("something went wrong")},
			StatusCode: 500,
		}, nil
	}

	return openapi.UpdateTemplate204Response{}, nil
}

// GetTemplates implements openapi.StrictServerInterface.
func (h *Handler) GetTemplates(ctx context.Context, _ openapi.GetTemplatesRequestObject) (openapi.GetTemplatesResponseObject, error) {
	templates, err := h.tmplSrv.ListTemplate(ctx)
	if err != nil {
		return openapi.GetTemplatesdefaultJSONResponse{
			Body:       openapi.ErrorResponse{StatusMessage: strPtr("failed to retrieve templates")},
			StatusCode: 500,
		}, nil
	}

	// Convert domain templates to OpenAPI response format
	var response openapi.TemplatesResponse
	for _, template := range templates {
		templateCopy := template // Create a copy to avoid issues with loop variable
		response = append(response, *ToOpenAPIModel(&templateCopy))
	}

	return openapi.GetTemplates200JSONResponse(response), nil
}
