package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/adapter/httpapi"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/adapter/resthttp"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/service"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/openapi"
)

// TestHTTPHandler_Integration tests the HTTP handler with a mock API backend
func TestHTTPHandler_Integration(t *testing.T) {
	// Skip if Docker is not available
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	ctx := context.Background()

	// Start a mock API container
	mockAPIContainer, baseURL := startMockAPIContainer(t, ctx)
	defer func() {
		if mockAPIContainer != nil {
			mockAPIContainer.Terminate(ctx)
		}
	}()

	// Create resty client and API adapter
	client := resty.New()
	templateAPI := httpapi.NewTemplateAPI(baseURL, client)

	// Create the service and handler
	templateSrv := service.NewTemplateSrv(templateAPI)
	handler := resthttp.NewHandler(templateSrv)

	// Create a router with the handler
	router := chi.NewRouter()
	// Convert StrictServerInterface to ServerInterface
	serverHandler := openapi.NewStrictHandler(handler, nil)
	// Use HandlerFromMuxWithBaseURL to strip the /api/v1 prefix from the routes
	router.Mount("/", openapi.HandlerFromMuxWithBaseURL(serverHandler, http.NewServeMux(), ""))

	// Test data
	connectionID := uuid.NewString()
	templateID := uuid.NewString()

	// Test GET template
	t.Run("GET /templates/{connectionId}/{templateId}", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/templates/%s/%s", connectionID, templateID), nil)
		rec := httptest.NewRecorder()

		router.ServeHTTP(rec, req)

		require.Equal(t, http.StatusNotFound, rec.Code)

		var response map[string]any
		_ = json.Unmarshal(rec.Body.Bytes(), &response)

		assert.NotEqual(t, templateID, response["template_id"])
		assert.NotEqual(t, connectionID, response["connection_id"])
	})

	// Test POST template
	t.Run("POST /templates/{connectionId}", func(t *testing.T) {
		template := map[string]any{
			"connection_id": connectionID,
			"is_default":    true,
			"body":          "Test receipt body",
			"header_text": map[string]string{
				"font":  "Arial",
				"style": "Bold",
				"color": "#000000",
			},
		}

		body, err := json.Marshal(template)
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodPost, "/templates/"+connectionID, strings.NewReader(string(body)))
		req.Header.Set("Content-Type", "application/json")
		rec := httptest.NewRecorder()

		router.ServeHTTP(rec, req)

		require.Equal(t, http.StatusNotFound, rec.Code)
	})

	// Test PUT template
	t.Run("PUT /templates/{connectionId}/{templateId}", func(t *testing.T) {
		template := map[string]any{
			"template_id":   templateID,
			"connection_id": connectionID,
			"is_default":    true,
			"body":          "Updated receipt body",
		}

		body, err := json.Marshal(template)
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodPut, fmt.Sprintf("/templates/%s/%s", connectionID, templateID), strings.NewReader(string(body)))
		req.Header.Set("Content-Type", "application/json")
		rec := httptest.NewRecorder()

		router.ServeHTTP(rec, req)

		require.Equal(t, http.StatusNotFound, rec.Code)
	})

	// Test DELETE template
	t.Run("DELETE /templates/{connectionId}/{templateId}", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/templates/%s/%s", connectionID, templateID), nil)
		rec := httptest.NewRecorder()

		router.ServeHTTP(rec, req)

		require.Equal(t, http.StatusNotFound, rec.Code)
	})
}

// startMockAPIContainer starts a container with a mock API for testing
func startMockAPIContainer(t *testing.T, ctx context.Context) (testcontainers.Container, string) {
	containerReq := testcontainers.ContainerRequest{
		Image:        "golang:1.22",
		ExposedPorts: []string{"8080/tcp"},
		Cmd: []string{"sh", "-c", `
cat <<EOF > /server.go
package main

import (
	"encoding/json"
	"io"
	"log"
	"net/http"
	"strings"
)

type Text struct {
	Font  string 
	Style string 
	Color string 
}

type Icon struct {
	Src    string 
	Width  int    
	Height int    
}

type Template struct {
	TemplateID   string 
	ConnectionID string 
	IsDefault    bool   
	Body         string 
	HeaderText   Text   
	FooterText   Text  
	HeaderIcon   Icon   
	FooterIcon   Icon   
	}

func main() {
	http.HandleFunc("/templates/", handleTemplates)
	http.HandleFunc("/health", handleHealth)
	
	log.Println("Starting server on :8080")
	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatalf("Server failed: %v", err)
	}
}

func handleHealth(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}

func handleTemplates(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	// Parse path to extract connectionID and templateID
	path := strings.TrimPrefix(r.URL.Path, "/templates/")
	parts := strings.Split(path, "/")
	
	connectionID := parts[0]
	var templateID string
	if len(parts) > 1 {
		templateID = parts[1]
	}
	
	// Skip UUID validation in the mock server to make testing easier
	switch r.Method {
	case "GET":
		// Return a template
		template := Template{
			TemplateID:   templateID,
			ConnectionID: connectionID,
			IsDefault:    true,
			Body:         "Receipt template body",
			HeaderText: Text{
				Font:  "Arial",
				Style: "Bold",
				Color: "#000000",
			},
			FooterText: Text{
				Font:  "Arial",
				Style: "Italic",
				Color: "#333333",
			},
			HeaderIcon: Icon{
				Src:    "header.png",
				Width:  100,
				Height: 50,
			},
			FooterIcon: Icon{
				Src:    "footer.png",
				Width:  80,
				Height: 40,
			},
		}
		json.NewEncoder(w).Encode(template)
		
	case "POST":
		// Create a new template
		body, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read request body", http.StatusBadRequest)
			return
		}
		
		var template Template
		if err := json.Unmarshal(body, &template); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}
		
		// Set response status
		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(template)
		
	case "PUT":
		// Update a template
		body, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read request body", http.StatusBadRequest)
			return
		}
		
		var template Template
		if err := json.Unmarshal(body, &template); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}
		
		// Set response status
		w.WriteHeader(http.StatusNoContent)
		
	case "DELETE":
		// Delete a template
		w.WriteHeader(http.StatusNoContent)
		
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}
EOF

go run /server.go
`},
		WaitingFor: wait.ForHTTP("/health").WithPort("8080/tcp"),
	}

	container, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: containerReq,
		Started:          true,
	})
	require.NoError(t, err)

	host, err := container.Host(ctx)
	require.NoError(t, err)

	port, err := container.MappedPort(ctx, "8080")
	require.NoError(t, err)

	baseURL := fmt.Sprintf("http://%s:%s", host, port.Port())

	// Wait for the server to be ready
	waitForLocalServer(t, baseURL+"/health")

	return container, baseURL
}

// waitForLocalServer waits for the local server to be ready
func waitForLocalServer(t *testing.T, healthURL string) {
	maxRetries := 30
	for i := 0; i < maxRetries; i++ {
		resp, err := http.Get(healthURL)
		if err == nil && resp.StatusCode == http.StatusOK {
			resp.Body.Close()

			return
		}
		if resp != nil {
			resp.Body.Close()
		}
		t.Logf("Waiting for server to start (attempt %d/%d)", i+1, maxRetries)
		// Sleep for a longer time before retrying
		time.Sleep(1 * time.Second)
	}
	t.Fatal("Server did not start in time")
}
