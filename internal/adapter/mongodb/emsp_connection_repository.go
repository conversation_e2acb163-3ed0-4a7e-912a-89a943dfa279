package mongodb

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/config"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-bff/internal/core/port"
)

// EMSPConnectionRepository implements the port.EMSPConnectionRepository interface
var _ port.EMSPConnectionRepository = (*EMSPConnectionRepository)(nil)

type EMSPConnectionRepository struct {
	client     *mongo.Client
	database   string
	collection string
}

// NewEMSPConnectionRepository creates a new instance of EMSPConnectionRepository
func NewEMSPConnectionRepository(client *mongo.Client, cfg *config.Mongodb) *EMSPConnectionRepository {
	return &EMSPConnectionRepository{
		client:     client,
		database:   cfg.Database,
		collection: cfg.Collection,
	}
}

// Exists checks if an EMSP connection exists with the given parameters
func (r *EMSPConnectionRepository) Exists(ctx context.Context, countryCode, partyID, cpoURL string) (bool, error) {
	collection := r.client.Database(r.database).Collection(r.collection)

	filter := bson.M{
		"country_code": countryCode,
		"party_code":   partyID,
		"cpo_url":      cpoURL,
	}

	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, fmt.Errorf("failed to check EMSP connection existence: %w", err)
	}

	return count > 0, nil
}
